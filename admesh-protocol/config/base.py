"""
Base configuration class for AdMesh Protocol
"""
import os
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod


class BaseConfig(ABC):
    """Base configuration class that all environment configs inherit from"""

    def __init__(self):
        self.environment = self.get_environment()
        self.validate_config()

    @property
    @abstractmethod
    def firebase_config(self) -> Dict[str, Any]:
        """Firebase configuration for this environment"""
        pass

    @property
    @abstractmethod
    def api_base_url(self) -> str:
        """API base URL for this environment"""
        pass

    @property
    @abstractmethod
    def frontend_url(self) -> str:
        """Frontend URL for this environment"""
        pass

    @property
    @abstractmethod
    def cors_origins(self) -> list:
        """CORS origins for this environment"""
        pass

    @property
    @abstractmethod
    def database_config(self) -> Dict[str, Any]:
        """Database configuration for this environment"""
        pass

    @property
    def debug(self) -> bool:
        """Debug mode setting"""
        return os.getenv("DEBUG", "false").lower() == "true"

    @property
    def log_level(self) -> str:
        """Logging level"""
        return os.getenv("LOG_LEVEL", "INFO").upper()

    @property
    def port(self) -> int:
        """Server port"""
        return int(os.getenv("PORT", "8000"))

    @property
    def host(self) -> str:
        """Server host"""
        return os.getenv("HOST", "127.0.0.1")

    @staticmethod
    def get_environment() -> str:
        """Detect the current environment"""
        # Check if running on Cloud Run
        is_cloud_run = os.getenv("K_SERVICE") is not None

        # Get explicit environment setting
        env = os.getenv("ENV", os.getenv("ENVIRONMENT", ""))

        if env:
            return env.lower()

        # Auto-detect based on deployment context
        if is_cloud_run:
            return "production"
        else:
            return "development"

    def validate_config(self):
        """Validate the configuration"""
        required_env_vars = self.get_required_env_vars()
        missing_vars = []

        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    @abstractmethod
    def get_required_env_vars(self) -> list:
        """Return list of required environment variables for this config"""
        pass

    def get_firebase_credentials_path(self) -> str:
        """Get the path to Firebase credentials"""
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        if credentials_path:
            return credentials_path

        # Default paths based on environment
        if self.environment == "development":
            return os.path.join(os.path.dirname(__file__), "..", "firebase", "dev-serviceAccountKey.json")
        else:
            return os.path.join(os.path.dirname(__file__), "..", "firebase", "serviceAccountKey.json")

    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration as a dictionary"""
        return {
            "environment": self.environment,
            "debug": self.debug,
            "log_level": self.log_level,
            "port": self.port,
            "host": self.host,
            "api_base_url": self.api_base_url,
            "frontend_url": self.frontend_url,
            "cors_origins": self.cors_origins,
            "firebase_config": self.firebase_config,
            "database_config": self.database_config,
            "firebase_credentials_path": self.get_firebase_credentials_path()
        }
