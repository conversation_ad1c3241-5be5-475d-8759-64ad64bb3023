#!/bin/bash
# Test environment runner

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🔄 Switching to test environment..."
python scripts/switch_env.py test

echo "🚀 Starting test server with network access..."
echo "📡 Server will be accessible on all network interfaces (0.0.0.0:8000)"
echo "🌐 Local access: http://localhost:8000"

# Get local IP address (macOS compatible)
LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
if [ -n "$LOCAL_IP" ]; then
    echo "🌐 Network access: http://$LOCAL_IP:8000"
else
    echo "🌐 Network access: http://[your-local-ip]:8000"
fi

python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
